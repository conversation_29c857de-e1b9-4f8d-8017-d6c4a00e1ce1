// src/hello_moveit.cpp
#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/point.hpp>
#include <moveit/move_group_interface/move_group_interface.h>
#include <moveit_msgs/msg/robot_trajectory.hpp>

#include <tf2/LinearMath/Vector3.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <tf2/LinearMath/Quaternion.h>
#if __has_include(<tf2_geometry_msgs/tf2_geometry_msgs.hpp>)
  #include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#else
  #include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#endif
#include <cmath>

using moveit::planning_interface::MoveGroupInterface;

static geometry_msgs::msg::Quaternion makeDownFacingQuat()
{
  // İstenen: EE'nin Z ekseni dünya -Z yönüne b<PERSON>ın (üstten yaklaşma).
  tf2::Vector3 z_w(0, 0, -1);     // hedef EE-Z
  tf2::Vector3 x_ref(1, 0, 0);    // referans eksen (yakın paralellik için kontrol edilecek)
  if (std::fabs(z_w.dot(x_ref)) > 0.99) x_ref = tf2::Vector3(0, 1, 0);

  tf2::Vector3 y_w = z_w.cross(x_ref); y_w.normalize();
  tf2::Vector3 x_w = y_w.cross(z_w);   x_w.normalize();

  // Kolonlar: [x_w  y_w  z_w]
  tf2::Matrix3x3 R(
    x_w.x(), y_w.x(), z_w.x(),
    x_w.y(), y_w.y(), z_w.y(),
    x_w.z(), y_w.z(), z_w.z()
  );
  tf2::Quaternion q; R.getRotation(q);
  return tf2::toMsg(q);
}

int main(int argc, char** argv)
{
  rclcpp::init(argc, argv);
  auto node = rclcpp::Node::make_shared("follow_point_node");

  // Parametreler
  std::string group_name = node->declare_parameter<std::string>("move_group", "manipulator");
  double pre_approach_dz = node->declare_parameter<double>("pre_approach_dz", 0.12);
  bool use_cartesian     = node->declare_parameter<bool>("use_cartesian", false); // isterse kartesyen düş

  MoveGroupInterface move_group(node, group_name);
  move_group.setPoseReferenceFrame("world");
  move_group.setPlanningTime(8.0);
  move_group.setNumPlanningAttempts(10);
  move_group.setGoalPositionTolerance(0.005);
  move_group.setGoalOrientationTolerance(0.05);
  move_group.setMaxVelocityScalingFactor(0.5);
  move_group.setMaxAccelerationScalingFactor(0.5);
  // move_group.setEndEffectorLink("end_effector_link"); // kendi EE link adınsa aç

  RCLCPP_INFO(node->get_logger(), "Planning frame: %s", move_group.getPlanningFrame().c_str());

  const auto q_down = makeDownFacingQuat();

  auto sub = node->create_subscription<geometry_msgs::msg::Point>(
    "/position", rclcpp::QoS(10),
    [&](const geometry_msgs::msg::Point& p)
    {
      move_group.setStartStateToCurrentState();
      move_group.clearPathConstraints();

      geometry_msgs::msg::Pose pre, goal;
      pre.position  = {p.x, p.y, p.z + pre_approach_dz};
      goal.position = {p.x, p.y, p.z};
      pre.orientation  = q_down;
      goal.orientation = q_down;

      if (use_cartesian) {
        // current -> pre -> goal şeklinde düz hatla in
        std::vector<geometry_msgs::msg::Pose> wps;
        auto cur = move_group.getCurrentPose().pose;
        // XY hizala (opsiyonel; istersen yorumdan çıkar)
        // cur.position.x = pre.position.x; cur.position.y = pre.position.y;
        cur.orientation = q_down;
        wps.push_back(cur);
        wps.push_back(pre);
        wps.push_back(goal);

        moveit_msgs::msg::RobotTrajectory traj;
        double fraction = move_group.computeCartesianPath(wps, 0.01, 0.0, traj);
        if (fraction > 0.95) {
          MoveGroupInterface::Plan plan_cart; plan_cart.trajectory_ = traj;
          auto ex = move_group.execute(plan_cart);
          if (ex != moveit::core::MoveItErrorCode::SUCCESS)
            RCLCPP_ERROR(node->get_logger(), "Kartezyen yürütme başarısız");
          else
            RCLCPP_INFO(node->get_logger(), "Kartezyen yol tamamlandı (%.1f%%)", fraction*100.0);
          return;
        } else {
          RCLCPP_WARN(node->get_logger(), "Kartezyen yol düşük (%.1f%%). Normal planlamaya geçiyorum.", fraction*100.0);
        }
      }

      // Normal planlama: pre -> goal
      move_group.setPoseTarget(pre);
      MoveGroupInterface::Plan plan_pre;
      if (move_group.plan(plan_pre) != moveit::core::MoveItErrorCode::SUCCESS) {
        RCLCPP_ERROR(node->get_logger(), "Pre-approach plan FAILED (%.3f, %.3f, %.3f)", pre.position.x, pre.position.y, pre.position.z);
        move_group.clearPoseTargets();
        return;
      }
      if (move_group.execute(plan_pre) != moveit::core::MoveItErrorCode::SUCCESS) {
        RCLCPP_ERROR(node->get_logger(), "Pre-approach execute FAILED");
        move_group.clearPoseTargets();
        return;
      }

      move_group.setPoseTarget(goal);
      MoveGroupInterface::Plan plan_goal;
      if (move_group.plan(plan_goal) != moveit::core::MoveItErrorCode::SUCCESS) {
        RCLCPP_ERROR(node->get_logger(), "Goal plan FAILED (%.3f, %.3f, %.3f)", goal.position.x, goal.position.y, goal.position.z);
        move_group.clearPoseTargets();
        return;
      }
      if (move_group.execute(plan_goal) != moveit::core::MoveItErrorCode::SUCCESS) {
        RCLCPP_ERROR(node->get_logger(), "Goal execute FAILED");
      } else {
        RCLCPP_INFO(node->get_logger(), "Reached goal: [%.3f, %.3f, %.3f]", goal.position.x, goal.position.y, goal.position.z);
      }
      move_group.clearPoseTargets();
    }
  );

  rclcpp::spin(node);
  rclcpp::shutdown();
  return 0;
}
